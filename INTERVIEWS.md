# Employee Discovery Interviews

## <PERSON> from Sales

**Role:** Regional Sales Director  
**Date:** March 15, 2025  
**Duration:** 32 minutes

**TPM:** Hey <PERSON>, thanks for taking the time.

**Marcus:** No problem, though I gotta say—talking about the expense system feels like therapy. [laughs] That thing has been the bane of my existence for years.

**TPM:** That bad?

**Marcus:** Well, not bad exactly. Just... unpredictable. Like, I can do the exact same trip twice and get completely different reimbursements. Makes no sense.

**TPM:** Can you give me an example?

**<PERSON>:** Sure. Last month I did Cleveland to Detroit, three days, maybe 180 miles of driving, decent hotel. Got reimbursed $847. Two weeks later, almost identical trip—Cleveland to Detroit again, three days, similar expenses—got $623. Same receipt total, same everything.

**TPM:** Any idea why?

**Marcus:** [shrugs] My theory? The system looks at the calendar. I swear it's more generous at certain times of the month. The first trip was early March, second was late March. Maybe it has monthly quotas or something?

**TPM:** Interesting. What about longer trips?

**<PERSON>:** Oh man, long trips are where it gets weird. Everyone says there's a sweet spot around 5-6 days, but I'm not convinced. I did an 8-day swing through Ohio and Indiana last year—tons of driving, hit like six cities—and the reimbursement was incredible. Way more than I expected.

But then <PERSON> from compliance did a 7-day conference in Denver, barely left the hotel, and she got peanuts. So maybe it's not about length, maybe it's about... I don't know, effort?

**TPM:** Effort?

**Marcus:** Like, how hard you're working. The system somehow knows if you're actually doing business or just coasting. That 8-day trip of mine? I was hitting 300+ miles some days, meetings morning to night. Maybe it rewards hustle?

**TPM:** That's an interesting theory.

**Marcus:** Could be totally wrong though. [laughs] I also thought for a while that it cared about which day of the week you submitted, but that turned out to be garbage. Though Kevin from procurement still swears by submitting on Tuesdays.

**TPM:** What about mileage specifically?

**Marcus:** Mileage is... complicated. Short drives, you get the standard rate, no surprises. But longer drives? It's like the system gets confused or something.

I did a 600-mile trip to Nashville once. Based on the rate for shorter trips, I expected like $350 in mileage reimbursement. Got $298. Not terrible, but definitely not linear.

**TPM:** So it drops off?

**Marcus:** Sort of? But then Dave from marketing did an 800-mile trip and swears he got more per mile than I did. Could be he's wrong about his mileage, or maybe there's some other factor. Distance bonuses? Time of year? Who knows.

**TPM:** Any patterns with receipts?

**Marcus:** [sighs] The receipt thing is the most frustrating part. I used to think higher receipts meant higher reimbursement, period. Makes sense, right?

Wrong. I've had $2,000 expense weeks that got me less than $1,200 weeks. There's definitely some kind of cap or penalty for spending too much, but nobody knows where it kicks in.

**TPM:** What's your theory?

**Marcus:** Honestly? I think it varies by person. Or department. Or maybe it's random. Sarah from ops says it's about daily spending rates, but I've tested that theory and it doesn't hold up.

Like, I had one trip where I kept it super modest—$60 a day in expenses. Got a decent reimbursement. Next trip, I went a little higher—$90 a day. Reimbursement was worse! Made no sense.

**TPM:** That is confusing.

**Marcus:** Right? And then there's the quarterly thing. End of Q4, the system is definitely more generous. I've seen it happen three years running. But Tom from HR says he doesn't see that pattern for candidate travel, so maybe it's just for sales trips?

**TPM:** Different rules for different departments?

**Marcus:** Maybe? Or maybe Tom's just not paying attention. [laughs] No offense to Tom.

But here's the weirdest thing—I swear the system remembers your history. Like, if you've been submitting a lot of big expense reports, it starts getting stingy. But if you keep it modest for a few months, it gets more generous.

**TPM:** That sounds pretty sophisticated.

**Marcus:** Could be coincidence. But I've started spacing out my big trips specifically because of this theory. Seems to work, but who knows?

**TPM:** Any other theories floating around?

**Marcus:** Oh, tons. There's the "magic number" theory—some people swear that certain receipt totals always get good reimbursements. Like, $847 is supposedly a lucky number, based on one person's anecdotal experience.

There's the "efficiency bonus" theory—that you get extra money for covering lots of ground in a short time. That one might actually be true.

And then there's the "rounding bug" theory—that if your receipts end in certain cents amounts, the system messes up the calculation in your favor. I've never tested that one.

**TPM:** Lots of theories.

**Marcus:** [laughs] That's the problem! Everyone has theories, nobody has answers. I just submit my stuff and hope for the best at this point.

**TPM:** Marcus, this has been really helpful. Thank you.

**Marcus:** Sure thing. And hey, if you figure out how that thing actually works, let me know. I'll buy you dinner.

---

## Lisa from Accounting

**Role:** Senior Staff Accountant  
**Date:** March 22, 2025  
**Duration:** 41 minutes

**TPM:** Hi Lisa, thanks for meeting with me.

**Lisa:** Of course! Though I have to warn you, I probably see this system from a different angle than most people. I'm the one who has to try to make sense of the numbers after the fact.

**TPM:** That's actually perfect. What patterns do you see?

**Lisa:** [laughs] Chaos, mostly. But there are some patterns, or at least things that look like patterns until you look closer.

Take the per diem calculation. Everyone assumes there's a standard daily rate, and mostly there is. $100 a day seems to be the base. But then there are these weird adjustments that nobody can explain.

**TPM:** What kind of adjustments?

**Lisa:** Well, 5-day trips almost always get a bonus. Not exactly sure how much, but it's consistent. 4-day trips, 6-day trips, normal rates. But 5 days? Always a little extra.

Except last week I saw a 5-day trip that didn't get the bonus. Same person who usually gets it, similar expenses. I have no idea what was different.

**TPM:** Could it be seasonal?

**Lisa:** Maybe? People keep saying the system has quarterly variations, but I track this stuff daily and I don't see clear patterns. End of quarter is definitely busier, more submissions, but the actual rates? 

I thought I saw a pattern last year where Q2 was more generous, but then Q2 this year has been totally normal. Could be coincidence.

**TPM:** What about mileage calculations?

**Lisa:** Oh, mileage is definitely tiered. First 100 miles or so, you get the full rate—like 58 cents per mile. After that, it drops.

But it's not a simple drop. I've tried to map it out in Excel, and it's some kind of curve. High-mileage trips still pay well, just not proportionally.

**TPM:** Any idea what the curve looks like?

**Lisa:** [sighs] I wish I knew. It's not linear, it's not a simple percentage drop. Sometimes I think it's logarithmic, but then I get a data point that doesn't fit.

Marcus from sales swears that 800-mile trips get better per-mile rates than 600-mile trips, but I've seen evidence that contradicts that. Could be other factors at play.

**TPM:** Like what?

**Lisa:** Trip length, maybe? Spending patterns? I've noticed that people who keep their expenses modest on long trips seem to do better on mileage reimbursement. But that could be my imagination.

**TPM:** What about receipt processing?

**Lisa:** That's where it gets really weird. There's definitely a cap on how much of your receipts get reimbursed, but it's not a hard cap.

Like, someone submits $1,000 in receipts, they might get $800 reimbursed. Someone else submits $1,200, they get $850. It's not proportional.

**TPM:** So diminishing returns?

**Lisa:** Yeah, but the curve is weird. Medium-high amounts—like $600-800—seem to get really good treatment. Higher than that, each dollar matters less and less.

And really low amounts get penalized. Like, if you submit $50 in receipts for a multi-day trip, you're better off submitting nothing. The reimbursement is often worse than just the base per diem.

**TPM:** That seems harsh.

**Lisa:** It does! And inconsistent. I've seen $30 receipt totals get decent reimbursements, and $80 totals get penalties. There might be some other factor—trip length, maybe, or total mileage—that affects how the receipt penalties work.

**TPM:** Have you noticed anything about trip categories?

**Lisa:** Categories? The system doesn't really categorize trips explicitly, but there do seem to be different calculation paths.

Quick trips with high mileage get treated differently than long trips with low mileage. But within those broad categories, there's still a lot of variation.

**TPM:** Different how?

**Lisa:** Well, the efficiency thing is real. People who cover a lot of ground in a short time get bonuses. But I can't figure out the exact calculation.

It's not just miles divided by days. I've seen 200 miles per day get a smaller bonus than 150 miles per day, depending on other factors.

**TPM:** Other factors?

**Lisa:** Spending, maybe? Trip length? Time of year? I honestly don't know. I've built like five different models trying to predict reimbursements, and none of them work consistently.

**TPM:** That must be frustrating.

**Lisa:** [laughs] It is! But also kind of fascinating. Like trying to solve a puzzle where someone keeps changing the rules.

**TPM:** Any theories about why it's so complex?

**Lisa:** I think it evolved over time. Started simple, then people kept adding rules and exceptions and adjustments. Now it's this weird hybrid system that nobody fully understands.

Or maybe it was designed to be unpredictable on purpose? Prevent gaming?

**TPM:** That's possible.

**Lisa:** The rounding thing is definitely intentional, though. Well, probably. If your receipts end in 49 or 99 cents, you often get a little extra money. Like the system rounds up twice or something.

**TPM:** That sounds like a bug.

**Lisa:** Could be! But it's been happening for years, so maybe it's a feature now. I've started timing my lunch purchases to hit those numbers. [laughs]

**TPM:** Any other observations?

**Lisa:** The variation is the most interesting part. Same person, same type of trip, different reimbursements. It's usually small differences—5-10%—but it's consistent.

Could be seasonal, could be some kind of randomization, could be factors we're not even considering. Market conditions? Company performance? Phase of the moon?

**TPM:** Market conditions?

**Lisa:** [shrugs] I'm grasping at straws. But the variation patterns don't look completely random. There's some kind of underlying logic, I just can't figure out what it is.

**TPM:** Lisa, this has been really insightful. Thank you.

**Lisa:** Happy to help! And hey, if you figure out the formula, can you share it? I'd love to finally understand what I'm looking at in these reports.

---

## Dave from Marketing

**Role:** Regional Marketing Manager  
**Date:** March 29, 2025  
**Duration:** 28 minutes

**TPM:** Dave, thanks for joining today.

**Dave:** Sure thing! Though I gotta say, I'm probably not the best person to ask about the expense system. I just submit my stuff and hope it works out.

**TPM:** That's actually a useful perspective. What's your experience been like?

**Dave:** Confusing, mostly. [laughs] Like, I went to this conference in Austin last year—4 days, normal expenses, maybe 100 miles of driving around the city. Got a really good reimbursement.

So I figured I had the system figured out. Next conference, similar setup in Phoenix. Way worse reimbursement. No idea why.

**TPM:** Any theories?

**Dave:** I thought maybe it was the city? Like, maybe the system has different rates for different places? But that seemed too complicated.

Then I thought maybe it was timing—the Austin trip was in May, Phoenix was in September. But Marcus from sales says he doesn't see seasonal patterns, so who knows.

**TPM:** What about distance?

**Dave:** Yeah, that's weird too. I drove to Chicago once—like 300 miles from here. Good mileage reimbursement, made sense.

But then I drove to Indianapolis, which is less far, and the mileage rate seemed higher per mile. Lisa from accounting says it's some kind of curve, but honestly, I just see randomness.

**TPM:** Do you track your expenses carefully?

**Dave:** I try to, but I'm not as systematic as some people. I know Kevin from procurement has like spreadsheets and theories about optimal submission timing and stuff.

I'm more of a "submit it and see what happens" person. Which probably makes me a bad interview subject for this. [laughs]

**TPM:** Not at all. What about longer trips?

**Dave:** I don't do many long trips, but the ones I have done were... inconsistent.

Did a week-long trade show circuit once—Chicago, Milwaukee, Minneapolis. Tons of driving, decent expenses. Reimbursement was okay, not great.

But Sarah from ops did something similar and got a huge bonus. She thinks it's because she hit some magic combination of days and miles and spending.

**TPM:** Magic combination?

**Dave:** That's her theory. Like, if you get the right numbers in all three categories, the system gives you a jackpot. But I've never hit it, so I can't confirm.

**TPM:** Have you noticed anything about receipt amounts?

**Dave:** Oh yeah, there's definitely something there. I learned early on not to submit tiny amounts. Like, if I just have a parking receipt for $12, I don't even bother. The reimbursement is usually worse than just leaving it off.

**TPM:** Worse how?

**Dave:** Like, if I submit nothing, I get the base per diem. If I submit $12 in receipts, I might get less than the per diem. Makes no sense, but I've seen it happen.

**TPM:** What about larger amounts?

**Dave:** Mixed results. I had one trip where I spent like $900—nice hotel, good dinners, some client entertainment. Got reimbursed for maybe $600 of it.

But then Kevin says he's had $1,200 expense weeks that got almost full reimbursement. So maybe it depends on the type of expenses? Or the trip length? Or Kevin's just making things up. [laughs]

**TPM:** Kevin seems to have a lot of theories.

**Dave:** Oh, he's obsessed with the system. He's got like charts and graphs trying to predict reimbursements. Last I heard, he was testing some theory about submission timing being tied to lunar cycles.

**TPM:** Lunar cycles?

**Dave:** [laughs] Yeah, I know how it sounds. But he swears he's found a pattern. End of the month is better than mid-month, new moon is better than full moon. Complete nonsense, but he's very committed to it.

**TPM:** What do you think actually drives the variations?

**Dave:** Honestly? I think it's partially random. Like, maybe the system was designed with some randomness to prevent gaming.

Or maybe it's just old and buggy and nobody knows how it works anymore. Legacy systems can be like that.

**TPM:** That's a reasonable theory.

**Dave:** I mean, I work in marketing, not accounting. My job is to make things look good, not understand complex algorithms. So I could be completely wrong.

But from a user experience perspective? The system feels arbitrary. Which is frustrating when you're trying to budget for trips.

**TPM:** Any advice for people using the system?

**Dave:** [laughs] Keep your expectations low? And maybe talk to Kevin if you want to go down the rabbit hole of optimization theories.

Personally, I just try to be reasonable with my expenses and submit everything promptly. Sometimes I win, sometimes I lose. As long as it averages out okay, I don't stress about it.

**TPM:** That seems like a healthy approach.

**Dave:** It's the only way to stay sane, honestly. I've seen people drive themselves crazy trying to optimize their reimbursements. Not worth it for the marginal gains.

**TPM:** Dave, thanks for the perspective. This was helpful.

**Dave:** No problem! And hey, if you can make the new system more predictable, that'd be great. Even if it's less generous, at least I'd know what to expect.

---

## Jennifer from HR

**Role:** HR Business Partner  
**Date:** April 8, 2025  
**Duration:** 35 minutes

**TPM:** Hi Jennifer, thanks for taking the time.

**Jennifer:** Of course! I'm always happy to help, especially with something that affects so many employees.

**TPM:** What's your perspective on the expense system?

**Jennifer:** Well, from an HR standpoint, it's... challenging. We get a lot of complaints about inconsistency, but when we try to investigate, we can't find clear patterns.

**TPM:** What kind of complaints?

**Jennifer:** Mostly around fairness. People see their colleagues get better reimbursements for similar trips and assume there's favoritism or errors.

But when we dig into the details, the trips are never actually identical. Different dates, different routes, different spending patterns. So it's hard to say if the system is being unfair or if there are just factors people aren't considering.

**TPM:** Do you think there are hidden factors?

**Jennifer:** Probably. The system is old and complex. I wouldn't be surprised if there are calculations happening that nobody fully understands anymore.

**TPM:** Have you noticed any patterns in the complaints?

**Jennifer:** A few things. New employees tend to get lower reimbursements at first, but that could be because they're not familiar with optimal practices yet.

Long-term employees seem to do better, but again, that could just be experience. They know to avoid certain pitfalls, time their submissions better, etc.

**TPM:** What pitfalls?

**Jennifer:** Well, the small receipts thing is real. We always warn new hires not to submit tiny expense amounts. Better to keep receipts over a certain threshold.

And timing seems to matter, though nobody agrees on the optimal timing. Some people swear by end-of-quarter submissions, others say mid-month is better.

**TPM:** What about trip length?

**Jennifer:** That's where we get the most complaints. People expect longer trips to get proportionally higher reimbursements, but that's not always the case.

There seems to be a sweet spot around 4-6 days where the reimbursements are particularly good. Shorter or longer than that, and people are often disappointed.

**TPM:** Any theories why?

**Jennifer:** I think the system was designed to encourage a certain type of business travel. Not too short that you're not really accomplishing anything, not so long that you're living it up on the company dime.

But that's just speculation. The actual calculations are opaque.

**TPM:** What about differences between departments?

**Jennifer:** That's interesting. Sales seems to do better overall, but they also travel more and probably understand the system better.

Finance and accounting folks are usually happy with their reimbursements, but they're also more conservative with their expenses.

Operations gets mixed results. They do a lot of different types of trips, so maybe the system treats them inconsistently.

**TPM:** Inconsistently how?

**Jennifer:** Well, Sarah from ops gets great reimbursements, but she's very strategic about her trips. She plans routes and timing specifically to optimize reimbursements.

Other people in ops just travel as needed and take whatever they get. Results vary a lot.

**TPM:** Strategic how?

**Jennifer:** I don't know the details, but she's got theories about optimal combinations of trip length, mileage, and spending. She treats it like a game.

**TPM:** That's an interesting approach.

**Jennifer:** It works for her, but it's also kind of absurd that employees need to become experts in expense optimization just to get fair reimbursements.

**TPM:** Fair point. Any other observations?

**Jennifer:** The variation is the biggest issue from an HR perspective. Even when we can't find obvious unfairness, the fact that similar trips get different reimbursements creates the perception of unfairness.

**TPM:** How do you handle those situations?

**Jennifer:** We explain that the system is complex and that there are many factors involved. We encourage people to be consistent with their travel practices and not to overthink it.

But honestly, it's frustrating. I'd love to be able to give people clear guidelines for maximizing their reimbursements, but I don't understand the system well enough myself.

**TPM:** What would you want in a new system?

**Jennifer:** Transparency, mostly. Even if the calculations are complex, people should be able to understand why they got the reimbursement they did.

And consistency. Similar trips should get similar reimbursements, unless there's a clear reason why they're different.

**TPM:** That makes sense.

**Jennifer:** The current system might be mathematically sophisticated, but it's a communication nightmare. Too much black box, not enough explanation.

**TPM:** Jennifer, this has been really helpful. Thank you.

**Jennifer:** You're welcome! And please, make the new system more user-friendly. Our employees deserve to understand how their reimbursements are calculated.

---

## Kevin from Procurement

**Role:** Senior Procurement Analyst  
**Date:** April 12, 2025  
**Duration:** 48 minutes

**TPM:** Kevin, I hear you've studied the expense system pretty extensively.

**Kevin:** [laughs] That's one way to put it. I prefer "obsessively analyzed." I've got spreadsheets going back three years tracking every reimbursement.

**TPM:** What have you found?

**Kevin:** Well, first off, most people are wrong about most things. The system is way more complex than anyone realizes, but it's also more logical than people think.

**TPM:** More logical?

**Kevin:** There are definitely patterns, you just have to look at the right factors. Everyone focuses on the obvious stuff—trip length, total expenses, total mileage. But those are just the starting points.

**TPM:** What else matters?

**Kevin:** Efficiency is huge. The system absolutely rewards high miles-per-day ratios. But it's not linear. There's a sweet spot around 180-220 miles per day where the bonuses are maximized.

Go too low, penalty. Go too high, the bonuses start dropping off again. Like the system thinks you're not actually doing business if you're driving 400 miles a day.

**TPM:** That's very specific.

**Kevin:** I've tested it! I specifically planned trips to hit different efficiency levels and tracked the results. The pattern is clear.

**TPM:** What about spending patterns?

**Kevin:** That's where most people mess up. They think more spending equals more reimbursement, but there are optimal spending ranges based on trip length.

Short trips, keep it under $75 per day. Medium trips—4-6 days—you can go up to $120 per day and still get good treatment. Long trips, you better keep it under $90 per day or you'll get penalized.

**TPM:** How did you figure that out?

**Kevin:** Trial and error, mostly. I've done probably 50 trips in the last three years, all carefully planned to test different variables.

**TPM:** That's... dedicated.

**Kevin:** [laughs] My wife thinks I'm crazy. But I've increased my average reimbursement by like 30% compared to before I started tracking.

**TPM:** What about timing?

**Kevin:** Oh, timing is massive. Everyone knows about end-of-quarter effects, but there are also monthly cycles, weekly cycles, even daily cycles.

**TPM:** Daily cycles?

**Kevin:** Tuesday submissions consistently outperform Monday submissions. Thursday is also good. Never submit on Friday—the system seems to be in a bad mood on Fridays.

**TPM:** That sounds... unlikely.

**Kevin:** I know how it sounds! But I've got the data. 247 submissions tracked by day of week. Tuesday is 8% higher on average than Friday.

**TPM:** Could be coincidence.

**Kevin:** Could be. But there's also the lunar cycle correlation.

**TPM:** Dave mentioned that.

**Kevin:** [excited] Dave's been talking about my research? That's great! Yeah, I've found a weak but statistically significant correlation between moon phases and reimbursement amounts.

New moon submissions average 4% higher than full moon submissions. It's not huge, but it's consistent.

**TPM:** What's your theory for why that would happen?

**Kevin:** Honestly? I think the system has some kind of randomization algorithm that's tied to external data sources. Maybe market indices, maybe astronomical data, maybe just pseudo-random number generators.

**TPM:** That seems elaborate.

**Kevin:** Everything about this system is elaborate! Did you know there are at least six different calculation paths depending on your trip characteristics?

**TPM:** Six paths?

**Kevin:** Well, that's my theory based on the clustering patterns I see in the data. Quick high-mileage trips get calculated differently than long low-mileage trips, which get calculated differently than medium balanced trips, etc.

**TPM:** How can you tell?

**Kevin:** Statistical analysis. I've done k-means clustering on all my data points, and they naturally separate into distinct groups with different reimbursement characteristics.

**TPM:** You did k-means clustering on expense data?

**Kevin:** [laughs] I know, I know. I might have gone a little overboard. But it works! I can predict my reimbursements within about 15% accuracy now.

**TPM:** That's impressive.

**Kevin:** The key insight is that it's not just about the individual factors—it's about the interactions between factors. Trip length times efficiency, spending per day times total mileage, stuff like that.

**TPM:** Interaction effects.

**Kevin:** Exactly! And there are threshold effects too. Certain combinations trigger bonuses, other combinations trigger penalties. It's like the system has these hidden decision trees.

**TPM:** Any specific combinations you've identified?

**Kevin:** Oh yeah. 5-day trips with 180+ miles per day and under $100 per day in spending—that's a guaranteed bonus. I call it the "sweet spot combo."

8+ day trips with high spending—that's a guaranteed penalty. I call it the "vacation penalty."

High mileage with low spending—usually good. Low mileage with high spending—usually bad.

**TPM:** You've really thought about this.

**Kevin:** It's fascinating! Like reverse-engineering a complex algorithm just from the outputs.

**TPM:** What about the randomness people mention?

**Kevin:** There's definitely some noise in the system, but I think it's intentional. Probably to prevent exactly what I'm doing—gaming the system through careful optimization.

But the noise is small enough that if you optimize the controllable factors, you still come out ahead on average.

**TPM:** Any other theories?

**Kevin:** I think the system has some kind of learning or adaptation component. My early trips when I first started tracking got different treatment than my recent trips, even controlling for all the factors I know about.

Could be that it builds a profile of each user and adjusts accordingly. Could be that it evolves over time. Hard to say without more data.

**TPM:** This is incredibly detailed, Kevin.

**Kevin:** [laughs] I warned you I was obsessed. But hey, if you're rebuilding the system, I'd love to help test it. I've got more data on this thing than probably anyone else in the company.

**TPM:** That might be very useful. Thank you.

**Kevin:** No problem! And if you want to see my spreadsheets, just let me know. I've got pivot tables that'll blow your mind. 